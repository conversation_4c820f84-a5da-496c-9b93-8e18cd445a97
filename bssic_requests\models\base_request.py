from odoo import models, fields, api, _
from odoo.exceptions import UserError
from lxml import etree


class BSSICBaseRequest(models.AbstractModel):
    """Base class for all BSSIC requests with common fields and methods"""
    _name = 'bssic.base.request'
    _description = 'BSSIC Base Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))

    # Employee fields
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                 help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)

    # Request basic info
    request_type_id = fields.Many2one('bssic.request.type', string='Request Type',
                                     required=True, tracking=True)
    request_type_code = fields.Char('Request Type Code', tracking=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State and workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('audit_manager', 'Audit Manager Approval'),
        ('it_manager', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'request_id', string='Activity Log')

    # Assignment and completion
    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 help="Select an IT staff member to assign this request to. Only employees with IT Staff permissions are shown.")
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    completion_notes = fields.Text('Completion Notes', tracking=True)

    # Computed fields for showing different field groups
    show_password_fields = fields.Boolean(string='Show Password Fields', compute='_compute_show_fields')
    show_usb_fields = fields.Boolean(string='Show USB Fields', compute='_compute_show_fields')
    show_extension_fields = fields.Boolean(string='Show Extension Fields', compute='_compute_show_fields')
    show_permission_fields = fields.Boolean(string='Show Permission Fields', compute='_compute_show_fields')
    show_email_fields = fields.Boolean(string='Show Email Fields', compute='_compute_show_fields')
    show_technical_fields = fields.Boolean(string='Show Technical Fields', compute='_compute_show_fields')
    show_authorization_delegation_fields = fields.Boolean(string='Show Authorization Delegation Fields', compute='_compute_show_fields')
    show_free_entry_fields = fields.Boolean(string='Show Free Entry Fields', compute='_compute_show_fields')

    # Technical request fields
    is_technical = fields.Boolean(string='Is Technical Request', default=False)
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)

    # Fields that need to be available in the main view (for compatibility)
    # Password reset fields
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    # USB request fields
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)

    # Extension request fields
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)

    # Email request fields
    email_type = fields.Selection([
        ('new', 'New Email'),
        ('password_reset', 'Password Reset'),
        ('2fa_reset', 'Two-Factor Authentication Reset'),
    ], string='Email Request Type', tracking=True)
    email_reason = fields.Text(string='Request Reason', tracking=True)
    email_agreement_text = fields.Html(string='Email Usage Agreement', readonly=True)
    email_agreement_accepted = fields.Boolean(string='I agree to the terms and conditions', default=False)

    # Authorization delegation fields
    ceiling_reason = fields.Text(string='Reason for Ceiling Increase', tracking=True)
    delegation_details = fields.Text(string='Details', tracking=True)
    delegation_max_amount = fields.Float(string='Max. Amount', tracking=True)
    delegation_auth_limit = fields.Float(string='Auth O.D. Limit', tracking=True)
    delegation_from_date = fields.Date(string='From Date', tracking=True)
    delegation_to_date = fields.Date(string='To Date', tracking=True)

    # Free entry fields
    free_entry_subject = fields.Char(string='Subject', tracking=True)
    free_entry_details = fields.Text(string='Operation Details', tracking=True)
    free_entry_from_date = fields.Date(string='From Date', tracking=True)
    free_entry_to_date = fields.Date(string='To Date', tracking=True)
    free_entry_user_name = fields.Char(string='User Name', tracking=True)
    free_entry_type = fields.Selection([
        ('free_entry', 'Free Entry'),
        ('reverse_entry', 'Reverse Entry')
    ], string='Entry Type', tracking=True)

    # Technical request fields
    request_nature = fields.Selection([
        ('technical', 'Technical'),
    ], string='Request Type', default='technical', tracking=True)
    technical_category_id = fields.Many2one('bssic.technical.category', string='Category', tracking=True)
    technical_subcategory_id = fields.Many2one('bssic.technical.category', string='Subcategory',
                                             domain="[('parent_id', '=', technical_category_id)]", tracking=True)
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent'),
    ], string='Priority', default='1', tracking=True)

    @api.depends('request_type_id')
    def _compute_show_fields(self):
        for record in self:
            record.show_password_fields = record.request_type_id.show_password_fields if record.request_type_id else False
            record.show_usb_fields = record.request_type_id.show_usb_fields if record.request_type_id else False
            record.show_extension_fields = record.request_type_id.show_extension_fields if record.request_type_id else False
            record.show_permission_fields = record.request_type_id.show_permission_fields if record.request_type_id else False
            record.show_email_fields = record.request_type_id.show_email_fields if record.request_type_id else False
            record.show_authorization_delegation_fields = record.request_type_id.show_authorization_delegation_fields if record.request_type_id else False
            record.show_free_entry_fields = record.request_type_id.show_free_entry_fields if record.request_type_id else False
            is_tech = record.request_type_id.code == 'technical' if record.request_type_id else False
            record.show_technical_fields = is_tech
            record.is_technical = is_tech

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    @api.constrains('assigned_to')
    def _check_assigned_to_it_staff(self):
        """Ensure assigned employee has IT Staff permissions"""
        for record in self:
            if record.assigned_to and record.assigned_to.user_id:
                if not record._validate_it_staff_assignment():
                    raise UserError(_(
                        'The selected employee "%s" does not have IT Staff permissions. '
                        'Please select an employee who is a member of the IT Staff group.'
                    ) % record.assigned_to.name)

    def _get_it_staff_domain(self):
        """Get domain for IT staff employees - only users with IT Staff group permission"""
        try:
            # Get IT Staff group
            it_staff_group = self.env.ref('bssic_requests.group_bssic_it_staff')

            # Get all users who have IT Staff group permission (including IT Managers)
            it_staff_users = self.env['res.users'].search([
                ('groups_id', 'in', [it_staff_group.id]),
                ('active', '=', True)
            ])

            # Get employees linked to these users
            it_staff_employees = self.env['hr.employee'].search([
                ('user_id', 'in', it_staff_users.ids),
                ('active', '=', True)
            ])

            # Log for debugging
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Found {len(it_staff_employees)} IT staff employees: {it_staff_employees.mapped('name')}")

            return [('id', 'in', it_staff_employees.ids)]

        except Exception as e:
            # Fallback to empty domain if there's an error
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error getting IT staff domain: {str(e)}")
            return [('id', '=', False)]

    @api.model
    def _get_employee_domain(self):
        """Get domain for employee selection based on user permissions"""
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

        if is_manager and employee:
            department_employees = self.env['hr.employee'].search([
                '|',
                ('id', '=', employee.id),
                '&',
                ('department_id', '=', employee.department_id.id),
                ('parent_id', '=', employee.id)
            ])
            return [('id', 'in', department_employees.ids)]
        else:
            if employee:
                return [('id', '=', employee.id)]
            else:
                return [('id', '=', -1)]

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        """Override to apply dynamic domains and permissions"""
        res = super(BSSICBaseRequest, self).fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Set domain for assigned_to field
            for node in doc.xpath("//field[@name='assigned_to']"):
                node.set('domain', str(self._get_it_staff_domain()))

            # Check if user is a manager
            is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

            # Set domain for employee_id field based on manager permissions
            employee_domain = self._get_employee_domain()
            employees = self.env['hr.employee'].search(employee_domain)

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                if is_manager:
                    node.set('domain', str([('id', 'in', employees.ids)]))
                else:
                    current_user = self.env.user
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))

                attrs = {'readonly': [('state', '!=', 'draft')]}
                node.set('attrs', str(attrs))

            # Make request_type_id readonly when specific request type is selected
            context = self.env.context
            if context.get('default_request_type_code') or context.get('default_request_type_id'):
                for node in doc.xpath("//field[@name='request_type_id']"):
                    node.set('readonly', '1')
                    node.set('force_save', '1')

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res

    @api.model
    def create(self, vals):
        """Override create to set sequence and request type"""
        # If request_type_code is provided but request_type_id is not, set request_type_id
        if vals.get('request_type_code') and not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([('code', '=', vals.get('request_type_code'))], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            request_type_name = ""
            if vals.get('request_type_id'):
                request_type = self.env['bssic.request.type'].browse(vals.get('request_type_id'))
                if request_type:
                    request_type_name = request_type.name

            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')

            if request_type_name:
                vals['name'] = f"{request_type_name} - {sequence}"
            else:
                vals['name'] = sequence

        return super(BSSICBaseRequest, self).create(vals)

    @api.model
    def default_get(self, fields_list):
        """Set default values including current user's employee"""
        res = super(BSSICBaseRequest, self).default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            if employee.int_id:
                res['employee_number'] = employee.int_id

        # Check if request_type_code is in context and set request_type_id accordingly
        context = self.env.context
        if context.get('default_request_type_code'):
            request_type_code = context.get('default_request_type_code')
            request_type = self.env['bssic.request.type'].search([('code', '=', request_type_code)], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id
                res['request_type_code'] = request_type_code

        if context.get('default_name'):
            res['name'] = context.get('default_name')

        return res

    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        """Handle employee number change with permission checks"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Handle employee selection with permission checks"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

        if self.employee_id:
            if is_manager:
                current_user = self.env.user
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            else:
                current_user = self.env.user
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                if self.employee_id.id == user_employee.id:
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}

    @api.onchange('request_type_code')
    def _onchange_request_type_code(self):
        """Set request type based on code"""
        if self.request_type_code:
            request_type = self.env['bssic.request.type'].search([('code', '=', self.request_type_code)], limit=1)
            if request_type:
                self.request_type_id = request_type.id

    # Helper methods for common operations
    def _get_group_users(self, group_xml_id):
        """Get users from a security group with error handling"""
        try:
            group = self.env.ref(group_xml_id)
            return group.users if group else self.env['res.users']
        except ValueError:
            # Log the error for debugging
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Security group {group_xml_id} not found")
            return self.env['res.users']

    def _notify_users(self, users, message, subscribe=True):
        """Send notification to users with validation"""
        if not users:
            return

        # Ensure users is a recordset
        if not hasattr(users, 'mapped'):
            users = self.env['res.users'].browse(users.id if hasattr(users, 'id') else [])

        partner_ids = users.mapped('partner_id.id')
        if partner_ids:
            try:
                if subscribe:
                    self.message_subscribe(partner_ids=partner_ids)
                self.message_post(body=message, partner_ids=partner_ids)
            except Exception as e:
                # Log notification errors but don't break the workflow
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"Failed to send notification: {str(e)}")

    def _create_activity_log(self, action, notes, old_state, new_state, assigned_to_id=None,
                           field_name=None, old_value=None, new_value=None):
        """Create enhanced activity log entry with detailed information"""
        if self.id:
            try:
                # Enhance notes with request details
                enhanced_notes = notes
                if hasattr(self, 'request_type_id') and self.request_type_id:
                    enhanced_notes = f"[{self.request_type_id.name}] {notes}"

                self.env['bssic.request.activity.log'].create_activity_log(
                    self._name, self.id, action, notes=enhanced_notes,
                    old_state=old_state, new_state=new_state,
                    assigned_to_id=assigned_to_id, field_name=field_name,
                    old_value=old_value, new_value=new_value
                )
            except Exception as e:
                # Log the error but don't break the workflow
                import logging
                _logger = logging.getLogger(__name__)
                _logger.error(f"Failed to create activity log: {str(e)}")

    def _log_field_change(self, field_name, old_value, new_value):
        """Log individual field changes"""
        if old_value != new_value:
            self._create_activity_log(
                'field_changed',
                f'Field "{field_name}" changed from "{old_value}" to "{new_value}"',
                None, None,
                field_name=field_name,
                old_value=old_value,
                new_value=new_value
            )

    def _check_user_permission(self, required_groups):
        """Check if current user has required permissions"""
        if not isinstance(required_groups, list):
            required_groups = [required_groups]

        for group_xml_id in required_groups:
            try:
                if self.env.user.has_group(group_xml_id):
                    return True
            except ValueError:
                continue
        return False

    def _validate_it_staff_assignment(self):
        """Validate that the assigned employee has IT Staff permissions"""
        if not self.assigned_to or not self.assigned_to.user_id:
            return False

        try:
            # Check if the assigned user has IT Staff group permission
            it_staff_group = self.env.ref('bssic_requests.group_bssic_it_staff')
            return it_staff_group in self.assigned_to.user_id.groups_id
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error validating IT staff assignment: {str(e)}")
            return False

    @api.model
    def create(self, vals):
        """Override create to log request creation"""
        try:
            result = super().create(vals)

            # Log request creation
            result._create_activity_log(
                'created',
                f'Request created with type: {result.request_type_id.name if result.request_type_id else "Unknown"}',
                None, 'draft'
            )

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Created request {result.name} of type {result.request_type_code}")

            return result
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Failed to create request: {str(e)}")
            raise

    def write(self, vals):
        """Override write to log field changes"""
        # Track important field changes before writing
        tracked_fields = {
            'state': 'State',
            'assigned_to': 'Assigned To',
            'priority': 'Priority',
            'completion_notes': 'Completion Notes',
            'rejection_reason': 'Rejection Reason',
            'request_type_id': 'Request Type',
            'employee_id': 'Employee',
        }

        # Store old values for comparison
        old_values = {}
        for record in self:
            for field_name, field_label in tracked_fields.items():
                if field_name in vals:
                    old_value = getattr(record, field_name)
                    if hasattr(old_value, 'name'):  # Many2one field
                        old_value = old_value.name
                    elif hasattr(old_value, 'mapped'):  # Many2many field
                        old_value = ', '.join(old_value.mapped('name'))
                    old_values[field_name] = str(old_value) if old_value else ''

        try:
            result = super().write(vals)

            # Log field changes after writing
            for record in self:
                for field_name, field_label in tracked_fields.items():
                    if field_name in vals and field_name in old_values:
                        new_value = getattr(record, field_name)
                        if hasattr(new_value, 'name'):  # Many2one field
                            new_value = new_value.name
                        elif hasattr(new_value, 'mapped'):  # Many2many field
                            new_value = ', '.join(new_value.mapped('name'))
                        new_value = str(new_value) if new_value else ''

                        if old_values[field_name] != new_value:
                            record._log_field_change(field_label, old_values[field_name], new_value)

            # Log state changes specifically
            if 'state' in vals:
                import logging
                _logger = logging.getLogger(__name__)
                for record in self:
                    _logger.info(f"Updated request {record.name} state to {vals['state']}")

            return result
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Failed to update request: {str(e)}")
            raise

    # Workflow methods
    def action_submit(self):
        """Submit request for approval"""
        self._create_activity_log(
            'submitted',
            _('Request submitted for approval'),
            'draft', 'direct_manager'
        )

        self.state = 'direct_manager'

        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self._notify_users(
                direct_manager.user_id,
                _('A new request has been submitted for your approval.')
            )

    def action_approve_direct_manager(self):
        """Approve request by direct manager"""
        # Check permissions
        if not self._check_user_permission('bssic_requests.group_bssic_direct_manager'):
            raise UserError(_('You do not have permission to approve requests as a direct manager.'))

        if self.is_technical or self.request_type_id.code == 'technical':
            # Technical requests skip audit manager
            self._create_activity_log(
                'direct_manager_approved',
                _('Approved by Direct Manager (Technical Request - Skip Audit)'),
                'direct_manager', 'it_manager'
            )
            self.state = 'it_manager'

            # Notify IT managers
            it_managers = self._get_group_users('bssic_requests.group_bssic_it_manager')
            self._notify_users(
                it_managers,
                _('This technical request has been approved by the direct manager and requires your approval.')
            )
        else:
            # Regular requests go to audit manager
            self._create_activity_log(
                'direct_manager_approved',
                _('Approved by Direct Manager'),
                'direct_manager', 'audit_manager'
            )
            self.state = 'audit_manager'

            # Notify audit managers
            audit_managers = self._get_group_users('bssic_requests.group_bssic_audit_manager')
            self._notify_users(
                audit_managers,
                _('This request has been approved by the direct manager and requires your approval.')
            )

    def action_approve_audit_manager(self):
        """Approve request by audit manager"""
        # Check permissions
        if not self._check_user_permission('bssic_requests.group_bssic_audit_manager'):
            raise UserError(_('You do not have permission to approve requests as an audit manager.'))

        self._create_activity_log(
            'audit_manager_approved',
            _('Approved by Audit Manager'),
            'audit_manager', 'it_manager'
        )
        self.state = 'it_manager'

        # Notify IT managers
        it_managers = self._get_group_users('bssic_requests.group_bssic_it_manager')
        self._notify_users(
            it_managers,
            _('This request has been approved by the audit manager and requires your approval.')
        )

    def action_approve_it_manager(self):
        """Approve request by IT manager"""
        # Check permissions
        if not self._check_user_permission('bssic_requests.group_bssic_it_manager'):
            raise UserError(_('You do not have permission to approve requests as an IT manager.'))

        self._create_activity_log(
            'it_manager_approved',
            _('Approved by IT Manager'),
            'it_manager', 'assigned'
        )
        self.state = 'assigned'

    def action_assign(self):
        """Assign request to IT staff"""
        # Check permissions
        if not self._check_user_permission('bssic_requests.group_bssic_it_manager'):
            raise UserError(_('You do not have permission to assign requests.'))

        if not self.assigned_to:
            raise UserError(_('Please select an IT staff member to assign this request to.'))

        # Validate that assigned employee has IT Staff permissions
        if not self._validate_it_staff_assignment():
            raise UserError(_('The selected employee does not have IT Staff permissions. Please select a valid IT staff member.'))

        self._create_activity_log(
            'assigned',
            _('Assigned to IT Staff: %s') % self.assigned_to.name,
            'assigned', 'in_progress',
            assigned_to_id=self.assigned_to.id
        )
        self.state = 'in_progress'

        # Notify assigned IT staff
        if self.assigned_to.user_id:
            self._notify_users(
                self.assigned_to.user_id,
                _('This request has been assigned to you for implementation.')
            )

    def action_complete(self):
        """Complete the request"""
        # Check permissions - IT staff or IT manager can complete
        if not self._check_user_permission(['bssic_requests.group_bssic_it_staff', 'bssic_requests.group_bssic_it_manager']):
            raise UserError(_('You do not have permission to complete requests.'))

        if not self.completion_notes:
            raise UserError(_('Please add completion notes before marking as completed.'))

        self._create_activity_log(
            'completed',
            _('Request completed. Notes: %s') % self.completion_notes,
            'in_progress', 'completed'
        )
        self.state = 'completed'

        # Notify employee who submitted the request
        if self.employee_id.user_id:
            self._notify_users(
                self.employee_id.user_id,
                _('Your request has been completed. Notes: %s') % self.completion_notes,
                subscribe=False  # Don't subscribe, just notify
            )

    def action_reject(self):
        """Reject the request"""
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id}
        }

    # Email agreement validation
    @api.onchange('email_type')
    def _onchange_email_type(self):
        """Set email agreement text when email type is 'new'"""
        if self.email_type == 'new':
            self.email_agreement_text = """
            <div style="direction: rtl; text-align: right;">
                <h3 style="text-align: center;">إقرار والتزام بضوابط استعمال البريد الإلكتروني</h3>

                <p>منظومة البريد الإلكتروني (bsicbank.com) لمصرف الساحل والصحراء للاستثمار والتجارة، تهدف الى إنجاز أعمال المصرف بجميع أشكالها في شكل توفير وسيلة التواصل بين إدارات المصرف فيما بينها من جانب، وكذلك تواصل بعض هذه الإدارات مع الجهات الاعتبارية الأخرى من جانب آخر وذلك وفق الصلاحيات الممنوحة وحسب التسلسل العملية الإدارية المعمول بها.</p>

                <h4 style="text-align: center;">وفق شروط وضوابط استخدام البريد الالكتروني التالية:</h4>

                <h4>المسؤولية ودواعي الاستعمال:</h4>
                <ol>
                    <li>البريد الإلكتروني الممنوح هو ملك لمصرف الساحل والصحراء، يخول صاحبه باستعماله بمفرده فيما استخدامه شؤون المصرف ذات العلاقة بوظيفته وأعمال إدارته أو إنجاز ما يطلب منه القيام به من عمل وعلى ذلك فإن جميع ما ينشأ أو ينظم من مراسلات ترجع ملكيتها للمصرف.</li>
                    <li>حساب البريد الممنوح للموظفين مخصص لهم أثناء تواجدهم للعمل بالإدارة العامة فقط، ويتم إيقاف حساب البريد في حالة إعارة الموظف إلى أحد الجهات التابعة الأخرى.</li>
                    <li>استعمال البريد الالكتروني يستوجب احترام التسلسل الإداري الوظيفي وان لا يتجاوز مستوى مديره المباشر الا بإذن مباشر منه وضمن رئاسته للبريد الالكتروني وبعلم جميع من يراسله.</li>
                    <li>عند التواصل مع مستخدم البريد الالكتروني يجب ان يكتب بأسلوب مهذب ومهني ومن شأن زملائه او الغير وان يتأكد من العنوان المرسل له قبل الإرسال حتى لا تصل المعلومات الى أشخاص لا علاقة لهم بموضوع المراسلة.</li>
                    <li>ان يقوم بالاطلاع على البريد بشكل مستمر ومنتظم ولا يعفى من المسؤولية عند التأخير في الإنجاز.</li>
                    <li>يتعهد بعد استعمال البريد في التواصل لأغراض شخصية بجميع أنواعها او تجارية، والا يقوم بإطلاع الغير على محتويات بريده والحفاظ على السرية التامة.</li>
                    <li>ألا يقوم بأعمال تؤدي الى ضرر الاخرين او انتحال صفة او شخصية غير التي يشغلها بالمصرف، قد تجر المصرف الى جهات قضائية، او تعويضات، او تشويه سمعة المصرف، او غيرها من الآثار المعنوية.</li>
                    <li>أن يقوم بالتأكد من الحصول على إذن من الإدارة المعنية بالمصرف قبل القيام بإرسال أي وثائق او ملفات قد ترجع ملكيتها لجهات أخرى حتى لا تقع على المصرف مخالفة حقوق النشر والملفية الخاصة للغير.</li>
                </ol>

                <p style="text-align: center; margin-top: 20px;">أقر أني قد اطلعت على الشروط المذكورة أعلاه والتي على دراية تامة بكل ما ورد فيها.</p>
            </div>
            """


# Legacy model compatibility for bssic.permission.request
class BSSICPermissionRequestLegacy(models.Model):
    """
    Legacy compatibility model for bssic.permission.request
    Redirects all operations to bssic.request
    """
    _name = 'bssic.permission.request'
    _description = 'BSSIC Permission Request (Legacy - Use bssic.request instead)'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with permission filter"""
        request_model = self.env['bssic.request']
        # Add filter for permission requests
        permission_args = args + [('request_type_id.code', '=', 'permission')]
        return request_model.search(permission_args, offset=offset, limit=limit, order=order, count=count)

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request"""
        # Ensure it's a permission request
        if 'request_type_code' not in vals:
            vals['request_type_code'] = 'permission'
        return self.env['bssic.request'].create(vals)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)
