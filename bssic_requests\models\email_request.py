from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class BSSICEmailRequest(models.Model):
    """Email Request Model"""
    _name = 'bssic.email.request'
    _description = 'BSSIC Email Request'
    _inherit = 'bssic.base.request'

    # Email request specific fields
    email_type = fields.Selection([
        ('new', 'New Email'),
        ('password_reset', 'Password Reset'),
        ('2fa_reset', 'Two-Factor Authentication Reset'),
    ], string='Email Request Type', tracking=True)
    email_reason = fields.Text(string='Request Reason', tracking=True)
    
    # Email agreement fields
    email_agreement_text = fields.Html(string='Email Usage Agreement', readonly=True)
    email_agreement_accepted = fields.Boolean(string='I agree to the terms and conditions', default=False)

    @api.onchange('email_type')
    def _onchange_email_type(self):
        """Set email agreement text when email type is 'new'"""
        if self.email_type == 'new':
            self.email_agreement_text = """
            <div style="direction: rtl; text-align: right;">
                <h3 style="text-align: center;">إقرار والتزام بضوابط استعمال البريد الإلكتروني</h3>

                <p>منظومة البريد الإلكتروني (bsicbank.com) لمصرف الساحل والصحراء للاستثمار والتجارة، تهدف الى إنجاز أعمال المصرف بجميع أشكالها في شكل توفير وسيلة التواصل بين إدارات المصرف فيما بينها من جانب، وكذلك تواصل بعض هذه الإدارات مع الجهات الاعتبارية الأخرى من جانب آخر وذلك وفق الصلاحيات الممنوحة وحسب التسلسل العملية الإدارية المعمول بها.</p>

                <h4 style="text-align: center;">وفق شروط وضوابط استخدام البريد الالكتروني التالية:</h4>

                <h4>المسؤولية ودواعي الاستعمال:</h4>
                <ol>
                    <li>البريد الإلكتروني الممنوح هو ملك لمصرف الساحل والصحراء، يخول صاحبه باستعماله بمفرده فيما استخدامه شؤون المصرف ذات العلاقة بوظيفته وأعمال إدارته أو إنجاز ما يطلب منه القيام به من عمل وعلى ذلك فإن جميع ما ينشأ أو ينظم من مراسلات ترجع ملكيتها للمصرف.</li>
                    <li>حساب البريد الممنوح للموظفين مخصص لهم أثناء تواجدهم للعمل بالإدارة العامة فقط، ويتم إيقاف حساب البريد في حالة إعارة الموظف إلى أحد الجهات التابعة الأخرى.</li>
                    <li>استعمال البريد الالكتروني يستوجب احترام التسلسل الإداري الوظيفي وان لا يتجاوز مستوى مديره المباشر الا بإذن مباشر منه وضمن رئاسته للبريد الالكتروني وبعلم جميع من يراسله.</li>
                    <li>عند التواصل مع مستخدم البريد الالكتروني يجب ان يكتب بأسلوب مهذب ومهني ومن شأن زملائه او الغير وان يتأكد من العنوان المرسل له قبل الإرسال حتى لا تصل المعلومات الى أشخاص لا علاقة لهم بموضوع المراسلة.</li>
                    <li>ان يقوم بالاطلاع على البريد بشكل مستمر ومنتظم ولا يعفى من المسؤولية عند التأخير في الإنجاز.</li>
                    <li>يتعهد بعد استعمال البريد في التواصل لأغراض شخصية بجميع أنواعها او تجارية، والا يقوم بإطلاع الغير على محتويات بريده والحفاظ على السرية التامة.</li>
                    <li>ألا يقوم بأعمال تؤدي الى ضرر الاخرين او انتحال صفة او شخصية غير التي يشغلها بالمصرف، قد تجر المصرف الى جهات قضائية، او تعويضات، او تشويه سمعة المصرف، او غيرها من الآثار المعنوية.</li>
                    <li>أن يقوم بالتأكد من الحصول على إذن من الإدارة المعنية بالمصرف قبل القيام بإرسال أي وثائق او ملفات قد ترجع ملكيتها لجهات أخرى حتى لا تقع على المصرف مخالفة حقوق النشر والملفية الخاصة للغير.</li>
                </ol>

                <p style="text-align: center; margin-top: 20px;">أقر أني قد اطلعت على الشروط المذكورة أعلاه والتي على دراية تامة بكل ما ورد فيها.</p>
            </div>
            """

    @api.constrains('state', 'email_type', 'email_agreement_accepted')
    def _check_email_agreement(self):
        """Validate email agreement acceptance for new email requests"""
        for record in self:
            if record.email_type == 'new' and not record.email_agreement_accepted and record.state != 'draft':
                raise ValidationError(_("يجب الموافقة على شروط استخدام البريد الإلكتروني للمتابعة في طلب البريد الإلكتروني الجديد."))

    @api.constrains('email_type', 'email_reason', 'state', 'show_email_fields')
    def _check_required_email_fields(self):
        """Validate required fields for email requests"""
        for record in self:
            if record.show_email_fields and record.state != 'draft':
                if not record.email_type:
                    raise UserError(_('Email Request Type is required for Email requests.'))
                if not record.email_reason:
                    raise UserError(_('Request Reason is required for Email requests.'))

    @api.model
    def create(self, vals):
        """Override create to set email specific defaults"""
        # Set request type code for email
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'email'
        
        # Find and set the email request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'email')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return super(BSSICEmailRequest, self).create(vals)

    @api.model
    def default_get(self, fields_list):
        """Set default values for email requests"""
        res = super(BSSICEmailRequest, self).default_get(fields_list)
        
        # Set default request type for email
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'email'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'email')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
